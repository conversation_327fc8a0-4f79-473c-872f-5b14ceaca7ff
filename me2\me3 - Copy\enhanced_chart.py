# Enhanced Chart Interface with Modern Features
import pyqtgraph as pg
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
import numpy as np

class ModernChartWidget(QWidget):
    """Enhanced chart widget with modern features"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_modern_chart()
        self.setup_drawing_tools()
        self.orders_on_chart = []
        self.positions_on_chart = []
        
    def setup_modern_chart(self):
        """Setup modern chart interface"""
        layout = QVBoxLayout(self)
        
        # Chart toolbar
        toolbar = self.create_chart_toolbar()
        layout.addWidget(toolbar)
        
        # Main chart area with splitter for indicators
        self.chart_splitter = QSplitter(Qt.Vertical)
        
        # Main price chart
        self.main_chart = self.create_main_chart()
        self.chart_splitter.addWidget(self.main_chart)
        
        # Volume chart
        self.volume_chart = self.create_volume_chart()
        self.chart_splitter.addWidget(self.volume_chart)
        
        # Indicator charts (RSI, MACD, etc.)
        self.indicator_charts = {}
        
        # Set initial sizes (main chart 70%, volume 15%, indicators 15%)
        self.chart_splitter.setSizes([700, 150, 150])
        
        layout.addWidget(self.chart_splitter)
        
        # Chart status bar
        status_bar = self.create_chart_status_bar()
        layout.addWidget(status_bar)
    
    def create_chart_toolbar(self):
        """Create enhanced chart toolbar"""
        toolbar = QWidget()
        toolbar.setFixedHeight(50)
        toolbar.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #3d3d3d, stop:1 #2d2d2d);
                border-bottom: 1px solid #555;
            }
        """)
        
        layout = QHBoxLayout(toolbar)
        
        # Symbol selector
        self.symbol_combo = QComboBox()
        self.symbol_combo.setMinimumWidth(150)
        self.symbol_combo.setStyleSheet("""
            QComboBox {
                background-color: #404040;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px;
                color: white;
                font-weight: bold;
            }
        """)
        layout.addWidget(QLabel("Symbol:"))
        layout.addWidget(self.symbol_combo)
        
        layout.addWidget(self.create_separator())
        
        # Timeframe buttons
        timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
        self.timeframe_group = QButtonGroup()
        for tf in timeframes:
            btn = QPushButton(tf)
            btn.setCheckable(True)
            btn.setFixedSize(40, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:checked {
                    background-color: #00ff44;
                    color: black;
                }
                QPushButton:hover {
                    background-color: #555;
                }
            """)
            self.timeframe_group.addButton(btn)
            layout.addWidget(btn)
        
        # Set default timeframe
        self.timeframe_group.buttons()[2].setChecked(True)  # 15m default
        
        layout.addWidget(self.create_separator())
        
        # Chart type selector
        chart_types = ["Candlestick", "Line", "OHLC", "Heikin Ashi"]
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(chart_types)
        self.chart_type_combo.setStyleSheet(self.symbol_combo.styleSheet())
        layout.addWidget(QLabel("Type:"))
        layout.addWidget(self.chart_type_combo)
        
        layout.addWidget(self.create_separator())
        
        # Drawing tools
        drawing_tools = [
            ("Trendline", "📈"),
            ("Horizontal", "➖"),
            ("Fibonacci", "📊"),
            ("Rectangle", "⬜"),
            ("Text", "📝")
        ]
        
        self.drawing_group = QButtonGroup()
        for name, icon in drawing_tools:
            btn = QPushButton(icon)
            btn.setToolTip(f"Draw {name}")
            btn.setCheckable(True)
            btn.setFixedSize(35, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    font-size: 14px;
                }
                QPushButton:checked {
                    background-color: #0099ff;
                }
                QPushButton:hover {
                    background-color: #555;
                }
            """)
            self.drawing_group.addButton(btn)
            layout.addWidget(btn)
        
        layout.addWidget(self.create_separator())
        
        # Indicator toggles
        indicators = [
            ("MA", "Moving Average"),
            ("BB", "Bollinger Bands"),
            ("RSI", "RSI"),
            ("MACD", "MACD"),
            ("VOL", "Volume")
        ]
        
        for short, full in indicators:
            btn = QPushButton(short)
            btn.setToolTip(f"Toggle {full}")
            btn.setCheckable(True)
            btn.setFixedSize(35, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:checked {
                    background-color: #ff6600;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #555;
                }
            """)
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # Chart controls
        controls_layout = QHBoxLayout()
        
        # Auto-scale button
        auto_scale_btn = QPushButton("Auto")
        auto_scale_btn.setToolTip("Auto-scale chart")
        auto_scale_btn.setFixedSize(40, 30)
        controls_layout.addWidget(auto_scale_btn)
        
        # Zoom controls
        zoom_in_btn = QPushButton("+")
        zoom_in_btn.setFixedSize(30, 30)
        zoom_out_btn = QPushButton("-")
        zoom_out_btn.setFixedSize(30, 30)
        controls_layout.addWidget(zoom_in_btn)
        controls_layout.addWidget(zoom_out_btn)
        
        layout.addLayout(controls_layout)
        
        return toolbar
    
    def create_separator(self):
        """Create toolbar separator"""
        sep = QFrame()
        sep.setFrameShape(QFrame.VLine)
        sep.setFixedHeight(30)
        sep.setStyleSheet("color: #555;")
        return sep
    
    def create_main_chart(self):
        """Create the main price chart"""
        chart_widget = QWidget()
        layout = QVBoxLayout(chart_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Chart title
        title_layout = QHBoxLayout()
        self.chart_title = QLabel("BTC/USDT")
        self.chart_title.setStyleSheet("""
            color: white;
            font-size: 16px;
            font-weight: bold;
            padding: 5px;
        """)
        title_layout.addWidget(self.chart_title)
        
        # Price info
        self.price_info = QLabel("$0.00 (0.00%)")
        self.price_info.setStyleSheet("""
            color: #00ff44;
            font-size: 14px;
            font-weight: bold;
            padding: 5px;
        """)
        title_layout.addWidget(self.price_info)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # Create plot widget
        self.price_plot = pg.PlotWidget()
        self.price_plot.setBackground('#1a1a1a')
        self.price_plot.showGrid(x=True, y=True, alpha=0.3)
        
        # Customize axes
        self.price_plot.getAxis('left').setPen(pg.mkPen('#555'))
        self.price_plot.getAxis('bottom').setPen(pg.mkPen('#555'))
        self.price_plot.getAxis('left').setTextPen(pg.mkPen('#ccc'))
        self.price_plot.getAxis('bottom').setTextPen(pg.mkPen('#ccc'))
        
        # Add crosshair
        self.crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('#777', width=1, style=Qt.DashLine))
        self.crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('#777', width=1, style=Qt.DashLine))
        self.price_plot.addItem(self.crosshair_v, ignoreBounds=True)
        self.price_plot.addItem(self.crosshair_h, ignoreBounds=True)
        
        # Connect mouse events
        self.price_plot.scene().sigMouseMoved.connect(self.on_mouse_moved)
        self.price_plot.scene().sigMouseClicked.connect(self.on_chart_clicked)
        
        layout.addWidget(self.price_plot, 1)
        
        return chart_widget
    
    def create_volume_chart(self):
        """Create volume chart"""
        volume_widget = QWidget()
        layout = QVBoxLayout(volume_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Volume title
        volume_title = QLabel("Volume")
        volume_title.setStyleSheet("""
            color: #0099ff;
            font-size: 12px;
            font-weight: bold;
            padding: 2px 5px;
        """)
        layout.addWidget(volume_title)
        
        # Volume plot
        self.volume_plot = pg.PlotWidget()
        self.volume_plot.setBackground('#1a1a1a')
        self.volume_plot.getAxis('left').setPen(pg.mkPen('#555'))
        self.volume_plot.getAxis('bottom').setPen(pg.mkPen('#555'))
        self.volume_plot.getAxis('left').setTextPen(pg.mkPen('#ccc'))
        self.volume_plot.getAxis('bottom').setTextPen(pg.mkPen('#ccc'))
        
        # Link x-axis with main chart
        self.volume_plot.getViewBox().setXLink(self.price_plot.getViewBox())
        
        layout.addWidget(self.volume_plot, 1)
        
        return volume_widget
    
    def create_chart_status_bar(self):
        """Create chart status bar"""
        status_bar = QWidget()
        status_bar.setFixedHeight(25)
        status_bar.setStyleSheet("""
            background-color: #2d2d2d;
            border-top: 1px solid #555;
        """)
        
        layout = QHBoxLayout(status_bar)
        layout.setContentsMargins(10, 2, 10, 2)
        
        # OHLC info
        self.ohlc_label = QLabel("O: 0.00 H: 0.00 L: 0.00 C: 0.00")
        self.ohlc_label.setStyleSheet("color: #ccc; font-size: 11px; font-family: monospace;")
        layout.addWidget(self.ohlc_label)
        
        layout.addStretch()
        
        # Volume info
        self.volume_label = QLabel("Vol: 0")
        self.volume_label.setStyleSheet("color: #0099ff; font-size: 11px; font-family: monospace;")
        layout.addWidget(self.volume_label)
        
        # Time info
        self.time_label = QLabel("Time: --:--:--")
        self.time_label.setStyleSheet("color: #ccc; font-size: 11px; font-family: monospace;")
        layout.addWidget(self.time_label)
        
        return status_bar
    
    def setup_drawing_tools(self):
        """Setup drawing tools functionality"""
        self.drawing_mode = None
        self.drawing_items = []
        self.temp_drawing_line = None
    
    def on_mouse_moved(self, pos):
        """Handle mouse movement for crosshair"""
        if self.price_plot.sceneBoundingRect().contains(pos):
            mouse_point = self.price_plot.getViewBox().mapSceneToView(pos)
            self.crosshair_v.setPos(mouse_point.x())
            self.crosshair_h.setPos(mouse_point.y())
            
            # Update status bar
            self.update_status_bar(mouse_point.x(), mouse_point.y())
    
    def on_chart_clicked(self, event):
        """Handle chart clicks for drawing and order placement"""
        if event.double():
            return
            
        mouse_point = self.price_plot.getViewBox().mapSceneToView(event.pos())
        price = mouse_point.y()
        
        # Check if in drawing mode
        if self.drawing_mode:
            self.handle_drawing_click(mouse_point, event)
        else:
            # Normal trading click
            self.handle_trading_click(price, event)
    
    def update_status_bar(self, x, y):
        """Update status bar with current values"""
        # You would implement this to show actual OHLC data at cursor position
        self.time_label.setText(f"Price: {y:.4f}")

class OrderVisualization:
    """Enhanced order visualization on chart"""
    
    def __init__(self, chart_widget):
        self.chart = chart_widget
        self.order_lines = {}
        self.position_lines = {}
    
    def add_order_line(self, order_id, price, side, quantity):
        """Add order line to chart"""
        color = '#28a745' if side == 'buy' else '#dc3545'
        line = pg.InfiniteLine(
            pos=price,
            angle=0,
            pen=pg.mkPen(color, width=2, style=Qt.DashLine),
            movable=True,
            label=f"{side.upper()} {quantity}@{price:.4f}"
        )
        
        self.chart.price_plot.addItem(line)
        self.order_lines[order_id] = line
        
        # Connect to move event to update order
        line.sigPositionChanged.connect(
            lambda: self.on_order_line_moved(order_id, line)
        )
    
    def remove_order_line(self, order_id):
        """Remove order line from chart"""
        if order_id in self.order_lines:
            self.chart.price_plot.removeItem(self.order_lines[order_id])
            del self.order_lines[order_id]
    
    def add_position_line(self, symbol, entry_price, size, pnl):
        """Add position line to chart"""
        color = '#00ff44' if pnl >= 0 else '#ff4444'
        side_text = "LONG" if size > 0 else "SHORT"
        
        line = pg.InfiniteLine(
            pos=entry_price,
            angle=0,
            pen=pg.mkPen(color, width=3),
            label=f"{side_text} {abs(size)}@{entry_price:.4f} P&L: {pnl:.2f}"
        )
        
        self.chart.price_plot.addItem(line)
        self.position_lines[symbol] = line
    
    def on_order_line_moved(self, order_id, line):
        """Handle order line movement to modify order"""
        new_price = line.value()
        # Emit signal to modify order
        print(f"Order {order_id} moved to price {new_price:.4f}")

class TradingHUD(QWidget):
    """Heads-up display for trading info"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_hud()
    
    def setup_hud(self):
        """Setup HUD overlay"""
        self.setFixedSize(300, 200)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        layout = QVBoxLayout(self)
        
        # Semi-transparent background
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 180);
                border: 1px solid rgba(255, 255, 255, 50);
                border-radius: 10px;
                color: white;
            }
        """)
        
        # Quick stats
        stats_layout = QGridLayout()
        
        # Equity
        stats_layout.addWidget(QLabel("Equity:"), 0, 0)
        self.equity_label = QLabel("$0.00")
        self.equity_label.setStyleSheet("color: #00ff44; font-weight: bold;")
        stats_layout.addWidget(self.equity_label, 0, 1)
        
        # P&L
        stats_layout.addWidget(QLabel("P&L:"), 1, 0)
        self.pnl_label = QLabel("$0.00")
        stats_layout.addWidget(self.pnl_label, 1, 1)
        
        # Open positions
        stats_layout.addWidget(QLabel("Positions:"), 2, 0)
        self.positions_label = QLabel("0")
        self.positions_label.setStyleSheet("color: #0099ff; font-weight: bold;")
        stats_layout.addWidget(self.positions_label, 2, 1)
        
        layout.addLayout(stats_layout)
        
        # Quick actions
        actions_layout = QHBoxLayout()
        
        close_all_btn = QPushButton("Close All")
        close_all_btn.setStyleSheet("""
            background-color: #ff6600;
            border: none;
            border-radius: 4px;
            padding: 8px;
            color: white;
            font-weight: bold;
        """)
        actions_layout.addWidget(close_all_btn)
        
        cancel_all_btn = QPushButton("Cancel All")
        cancel_all_btn.setStyleSheet("""
            background-color: #666;
            border: none;
            border-radius: 4px;
            padding: 8px;
            color: white;
            font-weight: bold;
        """)
        actions_layout.addWidget(cancel_all_btn)
        
        layout.addLayout(actions_layout)
    
    def update_stats(self, equity, pnl, positions_count):
        """Update HUD stats"""
        self.equity_label.setText(f"${equity:.2f}")
        
        self.pnl_label.setText(f"${pnl:.2f}")
        self.pnl_label.setStyleSheet(
            f"color: {'#28a745' if pnl >= 0 else '#dc3545'}; font-weight: bold;"
        )
        
        self.positions_label.setText(str(positions_count))