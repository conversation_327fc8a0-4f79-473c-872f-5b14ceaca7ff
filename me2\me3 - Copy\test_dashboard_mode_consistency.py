#!/usr/bin/env python3
"""
Test script to verify Dashboard tab mode consistency fix.
Tests that the dashboard maintains consistent data mode throughout the session.
"""

import sys
import os
import time
import threading

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_session_mode_consistency():
    """Test that session mode remains consistent throughout the session."""
    print("=" * 70)
    print("Dashboard Tab Mode Consistency Test")
    print("=" * 70)
    
    try:
        # Initialize the application
        import me2_stable
        
        from PySide6.QtWidgets import QApplication
        from dashboard_tab import DashboardTab
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ QApplication ready")
        
        # Test: Create Dashboard Tab and check session mode management
        print("\n🧪 Testing Dashboard Tab session mode management...")
        dashboard = DashboardTab()
        print("✅ Dashboard Tab created")
        
        # Check if session mode management components exist
        if hasattr(dashboard, '_get_session_mode'):
            print("✅ Session mode management method exists")
        else:
            print("❌ Session mode management method missing")
            return False
            
        if hasattr(dashboard, 'mode_indicator_label'):
            print("✅ Mode indicator label exists")
        else:
            print("❌ Mode indicator label missing")
            return False
        
        # Test initial session mode determination
        print("\n🔍 Testing initial session mode determination...")
        initial_mode = dashboard._get_session_mode()
        initial_reason = getattr(dashboard, '_session_mode_reason', 'Unknown')
        
        print(f"   Initial session mode: {initial_mode}")
        print(f"   Reason: {initial_reason}")
        
        # Check mode indicator
        indicator_text = dashboard.mode_indicator_label.text()
        print(f"   Mode indicator: {indicator_text}")
        
        if initial_mode == 'demo':
            expected_indicator = "📊 DEMO MODE"
        else:
            expected_indicator = "🔴 LIVE MODE"
        
        if expected_indicator in indicator_text:
            print("✅ Mode indicator correctly reflects session mode")
        else:
            print(f"❌ Mode indicator mismatch. Expected: {expected_indicator}, Got: {indicator_text}")
        
        # Test multiple refresh cycles to ensure consistency
        print("\n🔄 Testing mode consistency across multiple refresh cycles...")
        modes_observed = []
        
        for i in range(5):
            print(f"   Refresh cycle {i+1}/5...")
            
            # Trigger refresh
            dashboard.refresh_dashboard()
            
            # Wait for refresh to complete
            time.sleep(2)
            app.processEvents()
            
            # Check mode after refresh
            current_mode = dashboard._get_session_mode()
            modes_observed.append(current_mode)
            
            print(f"     Mode after refresh {i+1}: {current_mode}")
        
        # Verify all modes are the same
        unique_modes = set(modes_observed)
        if len(unique_modes) == 1:
            print("✅ Session mode remained consistent across all refresh cycles")
        else:
            print(f"❌ Session mode inconsistency detected! Observed modes: {unique_modes}")
            return False
        
        # Test API error handling
        print("\n🚨 Testing API error handling...")
        
        # Simulate API errors
        original_failure_count = getattr(dashboard, '_api_failure_count', 0)
        
        # Test error handling method
        if hasattr(dashboard, '_handle_api_error'):
            print("✅ API error handling method exists")
            
            # Simulate multiple API errors
            for i in range(3):
                should_fallback = dashboard._handle_api_error(f"Simulated API error {i+1}")
                print(f"   Error {i+1}: should_fallback = {should_fallback}")
                
                # Check that mode doesn't change
                current_mode = dashboard._get_session_mode()
                if current_mode == initial_mode:
                    print(f"   ✅ Mode remained {current_mode} after error {i+1}")
                else:
                    print(f"   ❌ Mode changed from {initial_mode} to {current_mode} after error {i+1}")
                    return False
        else:
            print("❌ API error handling method missing")
            return False
        
        # Test forced mode change (user action)
        print("\n🔧 Testing forced mode change (user action)...")
        
        if hasattr(dashboard, 'force_session_mode_change'):
            print("✅ Forced mode change method exists")
            
            # Test switching to opposite mode
            new_mode = 'live' if initial_mode == 'demo' else 'demo'
            dashboard.force_session_mode_change(new_mode, "Test mode change")
            
            # Verify mode changed
            changed_mode = dashboard._get_session_mode()
            if changed_mode == new_mode:
                print(f"✅ Mode successfully changed to {new_mode}")
                
                # Check indicator updated
                updated_indicator = dashboard.mode_indicator_label.text()
                print(f"   Updated indicator: {updated_indicator}")
                
                # Switch back to original mode
                dashboard.force_session_mode_change(initial_mode, "Test mode restore")
                restored_mode = dashboard._get_session_mode()
                
                if restored_mode == initial_mode:
                    print(f"✅ Mode successfully restored to {initial_mode}")
                else:
                    print(f"❌ Failed to restore mode. Expected: {initial_mode}, Got: {restored_mode}")
                    return False
            else:
                print(f"❌ Failed to change mode. Expected: {new_mode}, Got: {changed_mode}")
                return False
        else:
            print("❌ Forced mode change method missing")
            return False
        
        # Test data caching during API failures
        print("\n💾 Testing data caching during API failures...")
        
        # Check if caching attributes exist
        if hasattr(dashboard, '_last_good_account_data') or hasattr(dashboard, '_last_good_market_data'):
            print("✅ Data caching attributes exist")
        else:
            print("⚠️ Data caching attributes not yet initialized (will be created on first successful fetch)")
        
        # Clean up
        dashboard.deleteLater()
        
        print("\n" + "=" * 70)
        print("🎉 Dashboard Tab mode consistency test completed!")
        print("✅ Session mode management implemented correctly")
        print("✅ Mode consistency maintained across refresh cycles")
        print("✅ API error handling prevents unwanted mode switching")
        print("✅ Forced mode changes work for user actions")
        print("✅ Visual mode indicator updates correctly")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Dashboard mode consistency test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_demo_mode_flag_isolation():
    """Test that the dashboard doesn't rely on the global demo_mode flag."""
    print("\n🔒 Testing demo_mode flag isolation...")
    
    try:
        # Import and check the global demo_mode flag
        import me2_stable
        original_demo_mode = me2_stable.demo_mode
        
        print(f"   Original global demo_mode: {original_demo_mode}")
        
        # Create dashboard
        from dashboard_tab import DashboardTab
        dashboard = DashboardTab()
        
        # Get dashboard's session mode
        dashboard_mode = dashboard._get_session_mode()
        print(f"   Dashboard session mode: {dashboard_mode}")
        
        # Simulate global demo_mode flag change (this should NOT affect dashboard)
        me2_stable.demo_mode = not original_demo_mode
        print(f"   Changed global demo_mode to: {me2_stable.demo_mode}")
        
        # Check that dashboard mode is still consistent
        dashboard_mode_after = dashboard._get_session_mode()
        print(f"   Dashboard session mode after global change: {dashboard_mode_after}")
        
        if dashboard_mode == dashboard_mode_after:
            print("✅ Dashboard mode unaffected by global demo_mode flag changes")
            success = True
        else:
            print("❌ Dashboard mode changed when global demo_mode flag changed")
            success = False
        
        # Restore original demo_mode
        me2_stable.demo_mode = original_demo_mode
        print(f"   Restored global demo_mode to: {me2_stable.demo_mode}")
        
        # Clean up
        dashboard.deleteLater()
        
        return success
        
    except Exception as e:
        print(f"❌ Demo mode flag isolation test failed: {e}")
        return False

def main():
    """Main test function."""
    print("Starting Dashboard Tab mode consistency verification...")
    
    # Test 1: Session mode consistency
    consistency_success = test_session_mode_consistency()
    
    # Test 2: Demo mode flag isolation
    isolation_success = test_demo_mode_flag_isolation()
    
    if consistency_success and isolation_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Dashboard Tab mode consistency fix implemented successfully")
        print("✅ Session mode remains consistent throughout application runtime")
        print("✅ API errors handled with retry logic instead of mode switching")
        print("✅ Visual indicators provide clear mode feedback")
        print("✅ Professional, consistent data display achieved")
        return 0
    else:
        print("\n❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
