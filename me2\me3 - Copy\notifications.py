# Smart Notifications and Alerts System
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime, timedelta
import json

class NotificationCenter(QWidget):
    """Central notification management system"""
    
    notification_triggered = Signal(str, str, str)  # title, message, type
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.notifications = []
        self.setup_ui()
        self.setup_notification_rules()
    
    def setup_ui(self):
        """Setup notification center UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QHBoxLayout()
        title = QLabel("Notifications")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #00ff44;")
        header.addWidget(title)
        
        # Clear all button
        clear_btn = QPushButton("Clear All")
        clear_btn.setFixedSize(80, 25)
        clear_btn.clicked.connect(self.clear_all_notifications)
        header.addWidget(clear_btn)
        
        layout.addLayout(header)
        
        # Notification list
        self.notification_list = QListWidget()
        self.notification_list.setStyleSheet("""
            QListWidget {
                background-color: #1a1a1a;
                border: 1px solid #404040;
                border-radius: 4px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #333;
            }
            QListWidget::item:hover {
                background-color: #2a2a2a;
            }
        """)
        layout.addWidget(self.notification_list)
        
        # Notification settings
        settings_btn = QPushButton("Notification Settings")
        settings_btn.clicked.connect(self.show_settings)
        layout.addWidget(settings_btn)
    
    def setup_notification_rules(self):
        """Setup default notification rules"""
        self.rules = {
            'price_alerts': [],
            'pnl_alerts': {
                'profit_threshold': 100.0,
                'loss_threshold': -50.0,
                'enabled': True
            },
            'position_alerts': {
                'large_position_threshold': 1000.0,
                'enabled': True
            },
            'system_alerts': {
                'connection_issues': True,
                'order_failures': True,
                'margin_warnings': True
            }
        }
    
    def add_notification(self, title, message, notification_type='info', priority='normal'):
        """Add a new notification"""
        timestamp = datetime.now()
        notification = {
            'id': len(self.notifications),
            'title': title,
            'message': message,
            'type': notification_type,
            'priority': priority,
            'timestamp': timestamp,
            'read': False
        }
        
        self.notifications.append(notification)
        self.update_notification_list()
        
        # Show popup for high priority notifications
        if priority == 'high' or notification_type == 'error':
            self.show_popup_notification(notification)
        
        # Emit signal
        self.notification_triggered.emit(title, message, notification_type)
    
    def update_notification_list(self):
        """Update the notification list widget"""
        self.notification_list.clear()
        
        for notification in reversed(self.notifications[-50:]):  # Show last 50
            item = QListWidgetItem()
            widget = self.create_notification_widget(notification)
            item.setSizeHint(widget.sizeHint())
            
            self.notification_list.addItem(item)
            self.notification_list.setItemWidget(item, widget)
    
    def create_notification_widget(self, notification):
        """Create widget for a single notification"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Icon based on type
        icon_label = QLabel()
        icon_label.setFixedSize(20, 20)
        
        icons = {
            'info': '📝',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'trade': '💰',
            'alert': '🔔'
        }
        
        icon_label.setText(icons.get(notification['type'], '📝'))
        layout.addWidget(icon_label)
        
        # Content
        content_layout = QVBoxLayout()
        
        # Title and timestamp
        header_layout = QHBoxLayout()
        title_label = QLabel(notification['title'])
        title_label.setStyleSheet("font-weight: bold; color: white;")
        header_layout.addWidget(title_label)
        
        time_label = QLabel(notification['timestamp'].strftime("%H:%M:%S"))
        time_label.setStyleSheet("color: #888; font-size: 11px;")
        header_layout.addWidget(time_label)
        header_layout.addStretch()
        
        content_layout.addLayout(header_layout)
        
        # Message
        message_label = QLabel(notification['message'])
        message_label.setStyleSheet("color: #ccc; font-size: 12px;")
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)
        
        layout.addLayout(content_layout)
        
        # Priority indicator
        if notification['priority'] == 'high':
            priority_label = QLabel("!")
            priority_label.setFixedSize(15, 15)
            priority_label.setStyleSheet("""
                background-color: #dc3545;
                color: white;
                border-radius: 7px;
                text-align: center;
                font-weight: bold;
            """)
            layout.addWidget(priority_label)
        
        return widget
    
    def show_popup_notification(self, notification):
        """Show popup notification"""
        popup = NotificationPopup(notification, self.parent())
        popup.show()
    
    def clear_all_notifications(self):
        """Clear all notifications"""
        self.notifications.clear()
        self.update_notification_list()
    
    def show_settings(self):
        """Show notification settings dialog"""
        dialog = NotificationSettingsDialog(self.rules, self)
        if dialog.exec() == QDialog.Accepted:
            self.rules = dialog.get_rules()

class NotificationPopup(QWidget):
    """Popup notification widget"""
    
    def __init__(self, notification, parent=None):
        super().__init__(parent)
        self.notification = notification
        self.setup_popup()
        self.setup_animation()
    
    def setup_popup(self):
        """Setup popup appearance"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(350, 100)
        
        # Position at top-right of screen
        screen = QApplication.primaryScreen().geometry()
        self.move(screen.width() - 360, 10)
        
        layout = QHBoxLayout(self)
        
        # Background frame
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {'#dc3545' if self.notification['type'] == 'error' else '#28a745' if self.notification['type'] == 'success' else '#ffc107' if self.notification['type'] == 'warning' else '#17a2b8'};
                border-radius: 8px;
                border: 1px solid #555;
            }}
        """)
        
        frame_layout = QHBoxLayout(frame)
        
        # Icon
        icon_label = QLabel()
        icon_label.setFixedSize(40, 40)
        icon_label.setStyleSheet("font-size: 24px;")
        
        icons = {
            'info': '📝',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'trade': '💰',
            'alert': '🔔'
        }
        
        icon_label.setText(icons.get(self.notification['type'], '📝'))
        icon_label.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(icon_label)
        
        # Content
        content_layout = QVBoxLayout()
        
        title_label = QLabel(self.notification['title'])
        title_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        content_layout.addWidget(title_label)
        
        message_label = QLabel(self.notification['message'])
        message_label.setStyleSheet("color: white; font-size: 12px;")
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)
        
        frame_layout.addLayout(content_layout)
        
        # Close button
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                font-size: 18px;
                font-weight: bold;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 50);
            }
        """)
        close_btn.clicked.connect(self.close)
        frame_layout.addWidget(close_btn)
        
        layout.addWidget(frame)
        
        # Auto-close timer
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.close)
        self.close_timer.start(5000)  # Auto-close after 5 seconds
    
    def setup_animation(self):
        """Setup slide-in animation"""
        self.animation = QPropertyAnimation(self, b"pos")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Start position (off-screen)
        screen = QApplication.primaryScreen().geometry()
        start_pos = QPoint(screen.width(), 10)
        end_pos = QPoint(screen.width() - 360, 10)
        
        self.move(start_pos)
        self.animation.setStartValue(start_pos)
        self.animation.setEndValue(end_pos)
        self.animation.start()

class NotificationSettingsDialog(QDialog):
    """Dialog for configuring notification settings"""
    
    def __init__(self, rules, parent=None):
        super().__init__(parent)
        self.rules = rules.copy()
        self.setup_dialog()
    
    def setup_dialog(self):
        """Setup settings dialog"""
        self.setWindowTitle("Notification Settings")
        self.setFixedSize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # Tab widget for different categories
        tabs = QTabWidget()
        
        # P&L Alerts tab
        pnl_tab = QWidget()
        pnl_layout = QFormLayout(pnl_tab)
        
        self.pnl_enabled = QCheckBox("Enable P&L Alerts")
        self.pnl_enabled.setChecked(self.rules['pnl_alerts']['enabled'])
        pnl_layout.addRow(self.pnl_enabled)
        
        self.profit_threshold = QDoubleSpinBox()
        self.profit_threshold.setRange(1, 10000)
        self.profit_threshold.setValue(self.rules['pnl_alerts']['profit_threshold'])
        self.profit_threshold.setPrefix("$")
        pnl_layout.addRow("Profit Alert Threshold:", self.profit_threshold)
        
        self.loss_threshold = QDoubleSpinBox()
        self.loss_threshold.setRange(-10000, -1)
        self.loss_threshold.setValue(self.rules['pnl_alerts']['loss_threshold'])
        self.loss_threshold.setPrefix("$")
        pnl_layout.addRow("Loss Alert Threshold:", self.loss_threshold)
        
        tabs.addTab(pnl_tab, "P&L Alerts")
        
        # Price Alerts tab
        price_tab = QWidget()
        price_layout = QVBoxLayout(price_tab)
        
        price_layout.addWidget(QLabel("Price Alerts:"))
        
        self.price_alerts_list = QListWidget()
        price_layout.addWidget(self.price_alerts_list)
        
        # Add price alert controls
        add_price_layout = QHBoxLayout()
        add_price_layout.addWidget(QLabel("Symbol:"))
        self.price_symbol = QLineEdit()
        add_price_layout.addWidget(self.price_symbol)
        
        add_price_layout.addWidget(QLabel("Price:"))
        self.price_value = QDoubleSpinBox()
        self.price_value.setRange(0.0001, 1000000)
        self.price_value.setDecimals(4)
        add_price_layout.addWidget(self.price_value)
        
        add_price_btn = QPushButton("Add Alert")
        add_price_btn.clicked.connect(self.add_price_alert)
        add_price_layout.addWidget(add_price_btn)
        
        price_layout.addLayout(add_price_layout)
        
        tabs.addTab(price_tab, "Price Alerts")
        
        # System Alerts tab
        system_tab = QWidget()
        system_layout = QFormLayout(system_tab)
        
        self.connection_alerts = QCheckBox("Connection Issues")
        self.connection_alerts.setChecked(self.rules['system_alerts']['connection_issues'])
        system_layout.addRow(self.connection_alerts)
        
        self.order_failure_alerts = QCheckBox("Order Failures")
        self.order_failure_alerts.setChecked(self.rules['system_alerts']['order_failures'])
        system_layout.addRow(self.order_failure_alerts)
        
        self.margin_alerts = QCheckBox("Margin Warnings")
        self.margin_alerts.setChecked(self.rules['system_alerts']['margin_warnings'])
        system_layout.addRow(self.margin_alerts)
        
        tabs.addTab(system_tab, "System Alerts")
        
        layout.addWidget(tabs)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Load existing price alerts
        self.load_price_alerts()
    
    def load_price_alerts(self):
        """Load existing price alerts"""
        for alert in self.rules['price_alerts']:
            item_text = f"{alert['symbol']} @ ${alert['price']:.4f}"
            self.price_alerts_list.addItem(item_text)
    
    def add_price_alert(self):
        """Add new price alert"""
        symbol = self.price_symbol.text().strip()
        price = self.price_value.value()
        
        if symbol and price > 0:
            alert = {
                'symbol': symbol,
                'price': price,
                'condition': 'above'  # or 'below'
            }
            self.rules['price_alerts'].append(alert)
            
            item_text = f"{symbol} @ ${price:.4f}"
            self.price_alerts_list.addItem(item_text)
            
            self.price_symbol.clear()
            self.price_value.setValue(0)
    
    def get_rules(self):
        """Get updated rules"""
        self.rules['pnl_alerts']['enabled'] = self.pnl_enabled.isChecked()
        self.rules['pnl_alerts']['profit_threshold'] = self.profit_threshold.value()
        self.rules['pnl_alerts']['loss_threshold'] = self.loss_threshold.value()
        
        self.rules['system_alerts']['connection_issues'] = self.connection_alerts.isChecked()
        self.rules['system_alerts']['order_failures'] = self.order_failure_alerts.isChecked()
        self.rules['system_alerts']['margin_warnings'] = self.margin_alerts.isChecked()
        
        return self.rules

class AlertManager:
    """Manages different types of alerts and notifications"""
    
    def __init__(self, notification_center):
        self.notification_center = notification_center
        self.active_alerts = {}
        self.last_prices = {}
    
    def check_price_alerts(self, symbol, current_price):
        """Check if price alerts should be triggered"""
        for alert in self.notification_center.rules['price_alerts']:
            if alert['symbol'] == symbol:
                alert_id = f"price_{symbol}_{alert['price']}"
                
                if alert_id not in self.active_alerts:
                    if (alert['condition'] == 'above' and current_price >= alert['price']) or \
                       (alert['condition'] == 'below' and current_price <= alert['price']):
                        
                        self.notification_center.add_notification(
                            f"Price Alert: {symbol}",
                            f"{symbol} has reached ${current_price:.4f} ({alert['condition']} ${alert['price']:.4f})",
                            'alert',
                            'high'
                        )
                        
                        self.active_alerts[alert_id] = True
        
        self.last_prices[symbol] = current_price
    
    def check_pnl_alerts(self, total_pnl):
        """Check P&L threshold alerts"""
        rules = self.notification_center.rules['pnl_alerts']
        
        if not rules['enabled']:
            return
        
        if total_pnl >= rules['profit_threshold']:
            if 'profit_alert' not in self.active_alerts:
                self.notification_center.add_notification(
                    "Profit Target Reached",
                    f"Total P&L has reached ${total_pnl:.2f}",
                    'success',
                    'high'
                )
                self.active_alerts['profit_alert'] = True
        
        elif total_pnl <= rules['loss_threshold']:
            if 'loss_alert' not in self.active_alerts:
                self.notification_center.add_notification(
                    "Loss Limit Reached",
                    f"Total P&L has reached ${total_pnl:.2f}",
                    'error',
                    'high'
                )
                self.active_alerts['loss_alert'] = True
    
    def check_position_alerts(self, positions):
        """Check position-related alerts"""
        rules = self.notification_center.rules['position_alerts']
        
        if not rules['enabled']:
            return
        
        for position in positions:
            position_value = abs(position.get('notional', 0))
            
            if position_value >= rules['large_position_threshold']:
                alert_id = f"large_position_{position['symbol']}"
                
                if alert_id not in self.active_alerts:
                    self.notification_center.add_notification(
                        "Large Position Alert",
                        f"Large position detected: {position['symbol']} (${position_value:.2f})",
                        'warning',
                        'normal'
                    )
                    self.active_alerts[alert_id] = True
    
    def reset_alerts(self):
        """Reset active alerts"""
        self.active_alerts.clear()