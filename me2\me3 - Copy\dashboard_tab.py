#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Dashboard Tab
------------
Main dashboard tab for the trading interface, providing an overview of trading performance,
account status, and market conditions.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pyqtgraph as pg
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QPushButton, QComboBox, QTabWidget, QSplitter, QFrame,
    QDateEdit, QSpinBox, QDoubleSpinBox, QFileDialog, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, Signal, Slot
from PySide6.QtGui import QColor, QPen, QBrush, QFont
from performance_dashboard import PerformanceDashboard

class DashboardTab(QWidget):
    """
    Main dashboard tab for the trading interface.

    This tab provides a comprehensive overview of trading performance,
    account status, and market conditions.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.exchange = None
        self.initialized = False
        self._init_ui_components()

        # Initialize data
        self.account_info = {
            'equity': 0.0,
            'free_balance': 0.0,
            'used_margin': 0.0,
            'margin_ratio': 0.0,
            'unrealized_pnl': 0.0,
            'realized_pnl': 0.0
        }

        self.market_overview = {
            'btc_price': 0.0,
            'btc_24h_change': 0.0,
            'eth_price': 0.0,
            'eth_24h_change': 0.0,
            'total_market_cap': 0.0,
            'market_cap_change': 0.0,
            'fear_greed_index': 0
        }

        # Set up the UI
        self.setup_ui()

        # Set up update timer
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.refresh_dashboard)
        self.update_timer.start(30000)  # Update every 30 seconds

        # Initial refresh
        QTimer.singleShot(1000, self.refresh_dashboard)  # Refresh after 1 second to allow UI to initialize

    def _init_ui_components(self):
        """Initialize UI components"""
        layout = QVBoxLayout(self)
        
        # Add mode indicator
        self.mode_label = QLabel()
        self.mode_label.setStyleSheet("font-weight: bold; padding: 5px;")
        layout.addWidget(self.mode_label)
        
        # Add main content area
        content = QWidget()
        content_layout = QGridLayout(content)
        
        # Add performance dashboard
        from performance_dashboard import PerformanceDashboard
        self.performance_dashboard = PerformanceDashboard()
        content_layout.addWidget(self.performance_dashboard, 0, 0)
        
        layout.addWidget(content)
        self.setLayout(layout)

    def setup_ui(self):
        """Set up the dashboard UI"""
        main_layout = QVBoxLayout(self)

        # Create control panel for component visibility
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # Create main splitter for top and bottom sections
        self.main_splitter = QSplitter(Qt.Vertical)
        self.main_splitter.setHandleWidth(8)  # Make handles easier to grab
        self.main_splitter.setChildrenCollapsible(False)  # Prevent components from being collapsed to zero size

        # Top section: Account overview and market summary in a horizontal splitter
        self.top_splitter = QSplitter(Qt.Horizontal)
        self.top_splitter.setHandleWidth(8)
        self.top_splitter.setChildrenCollapsible(False)

        # Account overview panel
        self.account_group = self.create_account_overview_panel()
        self.account_group.setObjectName("account_panel")
        self.top_splitter.addWidget(self.account_group)

        # Market overview panel
        self.market_group = self.create_market_overview_panel()
        self.market_group.setObjectName("market_panel")
        self.top_splitter.addWidget(self.market_group)

        # Set initial sizes for top splitter (50/50)
        self.top_splitter.setSizes([500, 500])

        # Add top splitter to main splitter
        self.main_splitter.addWidget(self.top_splitter)

        # Bottom section: Performance dashboard
        self.performance_dashboard = PerformanceDashboard()
        self.performance_dashboard.setObjectName("performance_dashboard")
        self.main_splitter.addWidget(self.performance_dashboard)

        # Set initial sizes (40% top, 60% bottom)
        self.main_splitter.setSizes([400, 600])

        # Add main splitter to layout
        main_layout.addWidget(self.main_splitter)

        # Add bottom control bar with refresh button and layout options
        bottom_bar = QHBoxLayout()

        # Add refresh button
        refresh_btn = QPushButton("Refresh Dashboard")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #222;
                color: #00ff44;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QPushButton:hover {
                background-color: #333;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_dashboard)
        bottom_bar.addWidget(refresh_btn)

        # Add save layout button
        save_layout_btn = QPushButton("Save Layout")
        save_layout_btn.setStyleSheet("""
            QPushButton {
                background-color: #222;
                color: #00aaff;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QPushButton:hover {
                background-color: #333;
            }
        """)
        save_layout_btn.clicked.connect(self.save_layout)
        bottom_bar.addWidget(save_layout_btn)

        # Add reset layout button
        reset_layout_btn = QPushButton("Reset Layout")
        reset_layout_btn.setStyleSheet("""
            QPushButton {
                background-color: #222;
                color: #ff8800;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QPushButton:hover {
                background-color: #333;
            }
        """)
        reset_layout_btn.clicked.connect(self.reset_layout)
        bottom_bar.addWidget(reset_layout_btn)

        main_layout.addLayout(bottom_bar)

        # Try to load saved layout
        QTimer.singleShot(500, self.load_layout)  # Load layout after UI is fully initialized

    def create_control_panel(self):
        """Create a control panel for toggling component visibility"""
        control_panel = QGroupBox("Dashboard Components")
        control_panel.setMaximumHeight(60)  # Keep it compact

        control_layout = QHBoxLayout(control_panel)
        control_layout.setContentsMargins(5, 5, 5, 5)

        # Account panel toggle
        self.account_toggle = QPushButton("Account")
        self.account_toggle.setCheckable(True)
        self.account_toggle.setChecked(True)
        self.account_toggle.clicked.connect(lambda checked: self.toggle_component("account_panel", checked))
        self.account_toggle.setStyleSheet("""
            QPushButton {
                background-color: #222;
                color: #e6e6e6;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QPushButton:checked {
                background-color: #444;
                color: #00ff44;
                border: 1px solid #00ff44;
            }
            QPushButton:hover {
                background-color: #333;
            }
        """)
        control_layout.addWidget(self.account_toggle)

        # Market panel toggle
        self.market_toggle = QPushButton("Market")
        self.market_toggle.setCheckable(True)
        self.market_toggle.setChecked(True)
        self.market_toggle.clicked.connect(lambda checked: self.toggle_component("market_panel", checked))
        self.market_toggle.setStyleSheet("""
            QPushButton {
                background-color: #222;
                color: #e6e6e6;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QPushButton:checked {
                background-color: #444;
                color: #00ff44;
                border: 1px solid #00ff44;
            }
            QPushButton:hover {
                background-color: #333;
            }
        """)
        control_layout.addWidget(self.market_toggle)

        # Performance dashboard toggle
        self.performance_toggle = QPushButton("Performance")
        self.performance_toggle.setCheckable(True)
        self.performance_toggle.setChecked(True)
        self.performance_toggle.clicked.connect(lambda checked: self.toggle_component("performance_dashboard", checked))
        self.performance_toggle.setStyleSheet("""
            QPushButton {
                background-color: #222;
                color: #e6e6e6;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QPushButton:checked {
                background-color: #444;
                color: #00ff44;
                border: 1px solid #00ff44;
            }
            QPushButton:hover {
                background-color: #333;
            }
        """)
        control_layout.addWidget(self.performance_toggle)

        # Layout dropdown
        layout_label = QLabel("Layout:")
        layout_label.setStyleSheet("color: #e6e6e6;")
        control_layout.addWidget(layout_label)

        self.layout_combo = QComboBox()
        self.layout_combo.addItems(["Default", "Account Focus", "Market Focus", "Performance Focus"])
        self.layout_combo.currentIndexChanged.connect(self.apply_layout_preset)
        self.layout_combo.setStyleSheet("""
            QComboBox {
                background-color: #222;
                color: #e6e6e6;
                border: 1px solid #333;
                padding: 5px;
                border-radius: 2px;
            }
            QComboBox::drop-down {
                border: 0px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                background-color: #222;
                color: #e6e6e6;
                selection-background-color: #444;
            }
        """)
        control_layout.addWidget(self.layout_combo)

        # Add mode indicator
        control_layout.addStretch()  # Push mode indicator to the right
        self.mode_indicator_label = QLabel("📊 INITIALIZING...")
        self.mode_indicator_label.setStyleSheet("color: #ffaa00; font-weight: bold; background-color: #332200; padding: 5px; border-radius: 3px;")
        control_layout.addWidget(self.mode_indicator_label)

        return control_panel

    def toggle_component(self, component_name, visible):
        """Toggle the visibility of a dashboard component"""
        # Find the component by name
        component = self.findChild(QWidget, component_name)
        if component:
            component.setVisible(visible)

            # Update splitter sizes to redistribute space
            if component_name in ["account_panel", "market_panel"]:
                # For top components, adjust the top splitter
                if visible:
                    # Make both components equal size when showing
                    self.top_splitter.setSizes([500, 500])
                else:
                    # Give all space to the visible component
                    sizes = self.top_splitter.sizes()
                    if component_name == "account_panel":
                        self.top_splitter.setSizes([0, sizes[0] + sizes[1]])
                    else:
                        self.top_splitter.setSizes([sizes[0] + sizes[1], 0])
            elif component_name == "performance_dashboard":
                # For performance dashboard, adjust the main splitter
                if visible:
                    # Restore previous sizes
                    self.main_splitter.setSizes([400, 600])
                else:
                    # Give all space to the top section
                    sizes = self.main_splitter.sizes()
                    self.main_splitter.setSizes([sizes[0] + sizes[1], 0])

    def apply_layout_preset(self, index):
        """Apply a predefined layout preset"""
        if index == 0:  # Default
            self.reset_layout()
        elif index == 1:  # Account Focus
            self.account_toggle.setChecked(True)
            self.market_toggle.setChecked(False)
            self.performance_toggle.setChecked(True)
            self.toggle_component("account_panel", True)
            self.toggle_component("market_panel", False)
            self.toggle_component("performance_dashboard", True)
            self.main_splitter.setSizes([300, 700])
        elif index == 2:  # Market Focus
            self.account_toggle.setChecked(False)
            self.market_toggle.setChecked(True)
            self.performance_toggle.setChecked(True)
            self.toggle_component("account_panel", False)
            self.toggle_component("market_panel", True)
            self.toggle_component("performance_dashboard", True)
            self.main_splitter.setSizes([300, 700])
        elif index == 3:  # Performance Focus
            self.account_toggle.setChecked(True)
            self.market_toggle.setChecked(True)
            self.performance_toggle.setChecked(True)
            self.toggle_component("account_panel", True)
            self.toggle_component("market_panel", True)
            self.toggle_component("performance_dashboard", True)
            self.main_splitter.setSizes([200, 800])

    def save_layout(self):
        """Save the current layout configuration"""
        try:
            import json
            import os

            # Create layout data
            layout_data = {
                "account_visible": self.account_toggle.isChecked(),
                "market_visible": self.market_toggle.isChecked(),
                "performance_visible": self.performance_toggle.isChecked(),
                "top_splitter_sizes": self.top_splitter.sizes(),
                "main_splitter_sizes": self.main_splitter.sizes()
            }

            # Convert sizes to lists for JSON serialization
            layout_data["top_splitter_sizes"] = [s for s in layout_data["top_splitter_sizes"]]
            layout_data["main_splitter_sizes"] = [s for s in layout_data["main_splitter_sizes"]]

            # Save to file
            with open("dashboard_layout.json", "w") as f:
                json.dump(layout_data, f)

            print("Dashboard layout saved successfully")
        except Exception as e:
            print(f"Error saving dashboard layout: {e}")

    def load_layout(self):
        """Load a saved layout configuration"""
        try:
            import json
            import os

            if not os.path.exists("dashboard_layout.json"):
                print("No saved layout found, using default")
                return

            # Load from file
            with open("dashboard_layout.json", "r") as f:
                layout_data = json.load(f)

            # Apply visibility settings
            self.account_toggle.setChecked(layout_data.get("account_visible", True))
            self.market_toggle.setChecked(layout_data.get("market_visible", True))
            self.performance_toggle.setChecked(layout_data.get("performance_visible", True))

            self.toggle_component("account_panel", layout_data.get("account_visible", True))
            self.toggle_component("market_panel", layout_data.get("market_visible", True))
            self.toggle_component("performance_dashboard", layout_data.get("performance_visible", True))

            # Apply splitter sizes
            top_sizes = layout_data.get("top_splitter_sizes", [500, 500])
            main_sizes = layout_data.get("main_splitter_sizes", [400, 600])

            self.top_splitter.setSizes(top_sizes)
            self.main_splitter.setSizes(main_sizes)

            print("Dashboard layout loaded successfully")
        except Exception as e:
            print(f"Error loading dashboard layout: {e}")

    def reset_layout(self):
        """Reset to default layout"""
        # Show all components
        self.account_toggle.setChecked(True)
        self.market_toggle.setChecked(True)
        self.performance_toggle.setChecked(True)

        self.toggle_component("account_panel", True)
        self.toggle_component("market_panel", True)
        self.toggle_component("performance_dashboard", True)

        # Reset splitter sizes
        self.top_splitter.setSizes([500, 500])
        self.main_splitter.setSizes([400, 600])

        # Reset layout dropdown
        self.layout_combo.setCurrentIndex(0)

    def create_account_overview_panel(self):
        """Create the account overview panel"""
        account_group = QGroupBox("Account Overview")
        account_layout = QGridLayout(account_group)

        # Create account metric labels
        self.equity_label = QLabel("Equity: $0.00")
        self.free_balance_label = QLabel("Free Balance: $0.00")
        self.used_margin_label = QLabel("Used Margin: $0.00")
        self.margin_ratio_label = QLabel("Margin Ratio: 0.00%")
        self.unrealized_pnl_label = QLabel("Unrealized P&L: $0.00")
        self.realized_pnl_label = QLabel("Realized P&L: $0.00")

        # Style the labels
        metric_style = "color: #e6e6e6; font-size: 14px;"
        self.equity_label.setStyleSheet(metric_style)
        self.free_balance_label.setStyleSheet(metric_style)
        self.used_margin_label.setStyleSheet(metric_style)
        self.margin_ratio_label.setStyleSheet(metric_style)
        self.unrealized_pnl_label.setStyleSheet(metric_style)
        self.realized_pnl_label.setStyleSheet(metric_style)

        # Add labels to grid
        account_layout.addWidget(self.equity_label, 0, 0)
        account_layout.addWidget(self.free_balance_label, 0, 1)
        account_layout.addWidget(self.used_margin_label, 1, 0)
        account_layout.addWidget(self.margin_ratio_label, 1, 1)
        account_layout.addWidget(self.unrealized_pnl_label, 2, 0)
        account_layout.addWidget(self.realized_pnl_label, 2, 1)

        # Add margin gauge
        margin_gauge_group = QGroupBox("Margin Usage")
        margin_gauge_layout = QVBoxLayout(margin_gauge_group)

        self.margin_gauge = self.create_margin_gauge()
        margin_gauge_layout.addWidget(self.margin_gauge)

        account_layout.addWidget(margin_gauge_group, 0, 2, 3, 1)

        return account_group

    def create_market_overview_panel(self):
        """Create the market overview panel"""
        market_group = QGroupBox("Market Overview")
        market_layout = QGridLayout(market_group)

        # Create market metric labels
        self.btc_price_label = QLabel("BTC: $0.00")
        self.btc_change_label = QLabel("24h: 0.00%")
        self.eth_price_label = QLabel("ETH: $0.00")
        self.eth_change_label = QLabel("24h: 0.00%")
        self.market_cap_label = QLabel("Market Cap: $0.00T")
        self.market_cap_change_label = QLabel("24h: 0.00%")
        self.fear_greed_label = QLabel("Fear & Greed: 0")

        # Style the labels
        metric_style = "color: #e6e6e6; font-size: 14px;"
        self.btc_price_label.setStyleSheet(metric_style)
        self.btc_change_label.setStyleSheet(metric_style)
        self.eth_price_label.setStyleSheet(metric_style)
        self.eth_change_label.setStyleSheet(metric_style)
        self.market_cap_label.setStyleSheet(metric_style)
        self.market_cap_change_label.setStyleSheet(metric_style)
        self.fear_greed_label.setStyleSheet(metric_style)

        # Add labels to grid
        market_layout.addWidget(self.btc_price_label, 0, 0)
        market_layout.addWidget(self.btc_change_label, 0, 1)
        market_layout.addWidget(self.eth_price_label, 1, 0)
        market_layout.addWidget(self.eth_change_label, 1, 1)
        market_layout.addWidget(self.market_cap_label, 2, 0)
        market_layout.addWidget(self.market_cap_change_label, 2, 1)
        market_layout.addWidget(self.fear_greed_label, 3, 0, 1, 2)

        # Add market trend chart
        market_trend_group = QGroupBox("Market Trend")
        market_trend_layout = QVBoxLayout(market_trend_group)

        self.market_trend_chart = self.create_market_trend_chart()
        market_trend_layout.addWidget(self.market_trend_chart)

        market_layout.addWidget(market_trend_group, 0, 2, 4, 1)

        return market_group

    def create_margin_gauge(self):
        """Create a gauge widget for margin usage"""
        # Create a simple progress bar for now
        from PySide6.QtWidgets import QProgressBar

        gauge = QProgressBar()
        gauge.setRange(0, 100)
        gauge.setValue(0)
        gauge.setTextVisible(True)
        gauge.setFormat("%p% Used")
        gauge.setStyleSheet("""
            QProgressBar {
                border: 1px solid #444;
                border-radius: 5px;
                background-color: #222;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #00ff44, stop:0.7 #ffff00, stop:1 #ff4444);
                border-radius: 5px;
            }
        """)

        return gauge

    def create_market_trend_chart(self):
        """Create a chart for market trend"""
        chart = pg.PlotWidget()
        chart.setBackground('#1a1a1a')
        chart.showGrid(x=True, y=True, alpha=0.3)
        chart.setLabel('left', 'Price')
        chart.setLabel('bottom', 'Time')

        # Add a sample line for now
        x = list(range(100))
        y = [np.sin(i/10) * 10 + 50 for i in x]

        pen = pg.mkPen(color='#00ff44', width=2)
        chart.plot(x, y, pen=pen)

        return chart

    def update_account_info(self, account_info):
        """Update the account information display"""
        self.account_info = account_info

        # Update labels
        self.equity_label.setText(f"Equity: ${account_info.get('equity', 0):.2f}")
        self.free_balance_label.setText(f"Free Balance: ${account_info.get('free_balance', 0):.2f}")
        self.used_margin_label.setText(f"Used Margin: ${account_info.get('used_margin', 0):.2f}")
        self.margin_ratio_label.setText(f"Margin Ratio: {account_info.get('margin_ratio', 0):.2f}%")

        # Update PnL labels with color coding
        unrealized_pnl = account_info.get('unrealized_pnl', 0)
        realized_pnl = account_info.get('realized_pnl', 0)

        self.unrealized_pnl_label.setText(f"Unrealized P&L: ${unrealized_pnl:.2f}")
        self.realized_pnl_label.setText(f"Realized P&L: ${realized_pnl:.2f}")

        if unrealized_pnl > 0:
            self.unrealized_pnl_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        elif unrealized_pnl < 0:
            self.unrealized_pnl_label.setStyleSheet("color: #ff4444; font-size: 14px;")
        else:
            self.unrealized_pnl_label.setStyleSheet("color: #e6e6e6; font-size: 14px;")

        if realized_pnl > 0:
            self.realized_pnl_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        elif realized_pnl < 0:
            self.realized_pnl_label.setStyleSheet("color: #ff4444; font-size: 14px;")
        else:
            self.realized_pnl_label.setStyleSheet("color: #e6e6e6; font-size: 14px;")

        # Update margin gauge
        equity = account_info.get('equity', 0)
        used_margin = account_info.get('used_margin', 0)

        if equity > 0:
            margin_percentage = min(100, (used_margin / equity) * 100)
            self.margin_gauge.setValue(int(margin_percentage))
        else:
            self.margin_gauge.setValue(0)

    def update_market_overview(self, market_data):
        """Update the market overview display"""
        self.market_overview = market_data

        # Update labels
        self.btc_price_label.setText(f"BTC: ${market_data.get('btc_price', 0):.2f}")
        self.eth_price_label.setText(f"ETH: ${market_data.get('eth_price', 0):.2f}")
        self.market_cap_label.setText(f"Market Cap: ${market_data.get('total_market_cap', 0)/1e12:.2f}T")
        self.fear_greed_label.setText(f"Fear & Greed: {market_data.get('fear_greed_index', 0)}")

        # Update change labels with color coding
        btc_change = market_data.get('btc_24h_change', 0)
        eth_change = market_data.get('eth_24h_change', 0)
        market_cap_change = market_data.get('market_cap_change', 0)

        self.btc_change_label.setText(f"24h: {btc_change:+.2f}%")
        self.eth_change_label.setText(f"24h: {eth_change:+.2f}%")
        self.market_cap_change_label.setText(f"24h: {market_cap_change:+.2f}%")

        # Color code change labels
        if btc_change > 0:
            self.btc_change_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        elif btc_change < 0:
            self.btc_change_label.setStyleSheet("color: #ff4444; font-size: 14px;")

        if eth_change > 0:
            self.eth_change_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        elif eth_change < 0:
            self.eth_change_label.setStyleSheet("color: #ff4444; font-size: 14px;")

        if market_cap_change > 0:
            self.market_cap_change_label.setStyleSheet("color: #00ff44; font-size: 14px;")
        elif market_cap_change < 0:
            self.market_cap_change_label.setStyleSheet("color: #ff4444; font-size: 14px;")

        # Update market trend chart
        try:
            # Clear the chart
            self.market_trend_chart.clear()

            # Generate a new trend line based on current market data
            # This is a simple sine wave for demonstration
            # In a real implementation, you would use historical price data
            import numpy as np
            import time

            # Use current time as seed for the sine wave
            seed = int(time.time()) % 100
            x = list(range(100))

            # Generate a sine wave with amplitude based on BTC price volatility
            amplitude = abs(btc_change) * 0.5 + 5  # Ensure minimum amplitude
            y = [np.sin((i + seed)/10) * amplitude + 50 for i in x]

            # Set color based on market trend
            if btc_change > 0:
                pen = pg.mkPen(color='#00ff44', width=2)  # Green for positive
            else:
                pen = pg.mkPen(color='#ff4444', width=2)  # Red for negative

            # Plot the new line
            self.market_trend_chart.plot(x, y, pen=pen)
        except Exception as e:
            print(f"Error updating market trend chart: {e}")

    def refresh_dashboard(self):
        """Refresh all dashboard components"""
        # Import global variables - but maintain session consistency
        from me2_stable import exchange, fetch_account_info, fetch_open_positions, fetch_ticker, fetch_tickers

        # Get the session mode (consistent throughout the session)
        session_mode = self._get_session_mode()

        # Initialize account and market data
        account_info = {}
        market_data = {}

        if session_mode == 'demo':
            # Demo mode - use sample data
            account_info = {
                'equity': 10000.0,
                'free_balance': 7500.0,
                'used_margin': 2500.0,
                'margin_ratio': 25.0,
                'unrealized_pnl': 150.0,
                'realized_pnl': 300.0
            }

            # Sample market data
            market_data = {
                'btc_price': 50000.0,
                'btc_24h_change': 2.5,
                'eth_price': 3000.0,
                'eth_24h_change': -1.2,
                'total_market_cap': 2.1e12,
                'market_cap_change': 1.8,
                'fear_greed_index': 65
            }
        else:
            # Live mode - fetch real data from exchange
            try:
                print("Refreshing dashboard with live data...")

                # Fetch account info using the existing function
                acct_info = fetch_account_info(force_refresh=True)
                print(f"Account info fetched: {acct_info}")

                # Parse the formatted strings to get numeric values
                equity_str = acct_info.get('equity', '$0.00').replace('$', '').replace(',', '')
                free_str = acct_info.get('free_balance', '$0.00').replace('$', '').replace(',', '')
                used_str = acct_info.get('used_balance', '$0.00').replace('$', '').replace(',', '')

                try:
                    equity = float(equity_str)
                    free_balance = float(free_str)
                    used_margin = float(used_str)
                except ValueError:
                    print(f"Error converting account values to float: {equity_str}, {free_str}, {used_str}")
                    equity = 0.0
                    free_balance = 0.0
                    used_margin = 0.0

                # Calculate margin ratio
                margin_ratio = (used_margin / equity) * 100 if equity > 0 else 0

                # Fetch positions to calculate PnL
                positions = fetch_open_positions()
                unrealized_pnl = 0
                realized_pnl = 0

                # Calculate unrealized PnL from positions
                for pos in positions:
                    pos_pnl = float(pos.get('unrealizedPnl', 0))
                    unrealized_pnl += pos_pnl

                # Populate account info
                account_info = {
                    'equity': equity,
                    'free_balance': free_balance,
                    'used_margin': used_margin,
                    'margin_ratio': margin_ratio,
                    'unrealized_pnl': unrealized_pnl,
                    'realized_pnl': realized_pnl  # We don't have realized PnL yet
                }

                # Fetch market data
                try:
                    # Try to fetch tickers for BTC and ETH
                    symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT']
                    tickers = {}

                    # First try fetch_tickers function if available
                    try:
                        tickers = fetch_tickers(symbols)
                    except (NameError, TypeError):
                        # If fetch_tickers is not available, fetch each ticker individually
                        for symbol in symbols:
                            try:
                                ticker = fetch_ticker(symbol)
                                if ticker:
                                    tickers[symbol] = ticker
                            except Exception as e:
                                print(f"Error fetching ticker for {symbol}: {e}")

                    # Extract BTC data
                    btc_price = 0.0
                    btc_change = 0.0
                    if 'BTC/USDT:USDT' in tickers:
                        btc_ticker = tickers['BTC/USDT:USDT']
                        btc_price = btc_ticker.get('last', 0)
                        btc_change = btc_ticker.get('percentage', 0)

                    # Extract ETH data
                    eth_price = 0.0
                    eth_change = 0.0
                    if 'ETH/USDT:USDT' in tickers:
                        eth_ticker = tickers['ETH/USDT:USDT']
                        eth_price = eth_ticker.get('last', 0)
                        eth_change = eth_ticker.get('percentage', 0)

                    # If we couldn't get data from fetch_tickers, try direct exchange calls
                    if not tickers and exchange is not None:
                        try:
                            # Fetch BTC price
                            btc_ticker = exchange.fetch_ticker('BTC/USDT:USDT')
                            btc_price = btc_ticker.get('last', 0)
                            btc_change = btc_ticker.get('percentage', 0)

                            # Fetch ETH price
                            eth_ticker = exchange.fetch_ticker('ETH/USDT:USDT')
                            eth_price = eth_ticker.get('last', 0)
                            eth_change = eth_ticker.get('percentage', 0)
                        except Exception as e:
                            print(f"Error fetching tickers directly from exchange: {e}")

                    # Populate market data
                    market_data = {
                        'btc_price': btc_price,
                        'btc_24h_change': btc_change,
                        'eth_price': eth_price,
                        'eth_24h_change': eth_change,
                        'total_market_cap': 2.1e12,  # Placeholder - would need external API
                        'market_cap_change': 1.8,    # Placeholder - would need external API
                        'fear_greed_index': 50       # Placeholder - would need external API
                    }

                    print(f"Market data fetched: BTC=${btc_price} ({btc_change}%), ETH=${eth_price} ({eth_change}%)")
                except Exception as e:
                    print(f"Error fetching market data: {e}")
                    # Use default market data if fetch fails
                    market_data = {
                        'btc_price': 0.0,
                        'btc_24h_change': 0.0,
                        'eth_price': 0.0,
                        'eth_24h_change': 0.0,
                        'total_market_cap': 0.0,
                        'market_cap_change': 0.0,
                        'fear_greed_index': 0
                    }

                # Cache successful data for fallback use
                self._last_good_account_data = account_info.copy()
                self._last_good_market_data = market_data.copy()

                # Reset failure count on successful fetch
                self._api_failure_count = 0
            except Exception as e:
                print(f"Error refreshing dashboard with live data: {e}")
                import traceback
                traceback.print_exc()

                # Handle API error with retry logic instead of mode switching
                should_fallback = self._handle_api_error(str(e))

                if should_fallback:
                    print("Dashboard: Using fallback data due to persistent API errors")
                    # Use demo data as fallback
                    account_info = {
                        'equity': 10000.0,
                        'free_balance': 7500.0,
                        'used_margin': 2500.0,
                        'margin_ratio': 25.0,
                        'unrealized_pnl': 150.0,
                        'realized_pnl': 300.0
                    }
                    market_data = {
                        'btc_price': 50000.0,
                        'btc_24h_change': 2.5,
                        'eth_price': 3000.0,
                        'eth_24h_change': -1.2,
                        'total_market_cap': 2.1e12,
                        'market_cap_change': 1.8,
                        'fear_greed_index': 65
                    }
                else:
                    print("Dashboard: Using cached/fallback data while maintaining live mode")
                    # Use last known good data or minimal fallback data
                    account_info = getattr(self, '_last_good_account_data', {
                        'equity': 0.0,
                        'free_balance': 0.0,
                        'used_margin': 0.0,
                        'margin_ratio': 0.0,
                        'unrealized_pnl': 0.0,
                        'realized_pnl': 0.0
                    })
                    market_data = getattr(self, '_last_good_market_data', {
                        'btc_price': 0.0,
                        'btc_24h_change': 0.0,
                        'eth_price': 0.0,
                        'eth_24h_change': 0.0,
                        'total_market_cap': 0.0,
                        'market_cap_change': 0.0,
                        'fear_greed_index': 0
                    })

        # Update the UI
        self.update_account_info(account_info)
        self.update_market_overview(market_data)

        # Refresh the performance dashboard
        self.performance_dashboard.refresh_dashboard()

    def _get_session_mode(self):
        """Get the consistent session mode (demo or live) for the entire session."""
        # Initialize session mode if not already set
        if not hasattr(self, '_session_mode'):
            self._initialize_session_mode()

        return self._session_mode

    def _initialize_session_mode(self):
        """Initialize the session mode based on current application state."""
        from me2_stable import demo_mode, exchange

        # Determine initial session mode based on current state
        if exchange is not None and hasattr(exchange, 'apiKey') and exchange.apiKey and not demo_mode:
            self._session_mode = 'live'
            self._session_mode_reason = 'Valid API credentials detected'
        else:
            self._session_mode = 'demo'
            self._session_mode_reason = 'No valid API credentials or demo_mode=True'

        # Track API failures for retry logic
        self._api_failure_count = 0
        self._last_api_failure_time = 0

        print(f"Dashboard: Session mode initialized as '{self._session_mode}' - {self._session_mode_reason}")

        # Update the visual indicator
        self._update_mode_indicator()

    def _update_mode_indicator(self):
        """Update the visual mode indicator in the dashboard."""
        if hasattr(self, 'mode_indicator_label'):
            if self._session_mode == 'demo':
                self.mode_indicator_label.setText("📊 DEMO MODE")
                self.mode_indicator_label.setStyleSheet("color: #ff9900; font-weight: bold; background-color: #332200; padding: 5px; border-radius: 3px;")
            else:
                self.mode_indicator_label.setText("🔴 LIVE MODE")
                self.mode_indicator_label.setStyleSheet("color: #00ff44; font-weight: bold; background-color: #002200; padding: 5px; border-radius: 3px;")

    def _handle_api_error(self, error_msg):
        """Handle API errors with retry logic instead of switching to demo mode."""
        import time

        current_time = time.time()
        self._api_failure_count += 1
        self._last_api_failure_time = current_time

        print(f"Dashboard: API error #{self._api_failure_count}: {error_msg}")

        # Only switch to demo mode after multiple consecutive failures over a longer period
        if self._api_failure_count >= 5 and (current_time - self._last_api_failure_time) < 300:  # 5 minutes
            print("Dashboard: Multiple API failures detected, but maintaining session mode consistency")
            print("Dashboard: Using fallback data instead of switching modes")
            return False  # Don't switch modes

        return False  # Never switch modes during session

    def force_session_mode_change(self, new_mode, reason="User requested"):
        """Force a session mode change (only for explicit user actions)."""
        old_mode = getattr(self, '_session_mode', 'unknown')
        self._session_mode = new_mode
        self._session_mode_reason = reason
        self._api_failure_count = 0  # Reset failure count

        print(f"Dashboard: Session mode changed from '{old_mode}' to '{new_mode}' - {reason}")
        self._update_mode_indicator()

        # Refresh dashboard with new mode
        self.refresh_dashboard()

    def initialize(self, exchange_instance):
        """Initialize dashboard with exchange instance"""
        self.exchange = exchange_instance
        self.initialized = True
        if hasattr(self, 'performance_dashboard'):
            self.performance_dashboard.initialize(self.exchange)
        print(f"Dashboard initialized with exchange={self.exchange is not None}")
        self.refresh_dashboard()

    def refresh_dashboard(self):
        """Refresh all dashboard components"""
        if not self.initialized:
            print("Dashboard not fully initialized, skipping refresh")
            return
            
        if hasattr(self, 'performance_dashboard'):
            self.performance_dashboard.refresh_dashboard()
            
        # Update any other components that need refreshing
        self.update()
