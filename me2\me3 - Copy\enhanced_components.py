# Enhanced Dashboard Components
import pyqtgraph as pg
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *

class ModernDashboardWidget(QWidget):
    """Enhanced dashboard with modern cards and better data visualization"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_modern_dashboard()
    
    def setup_modern_dashboard(self):
        """Setup modern dashboard layout"""
        layout = QVBoxLayout(self)
        
        # Top metrics cards row
        metrics_layout = QHBoxLayout()
        
        # Account summary card
        self.account_card = self.create_metric_card(
            "Account Summary",
            [
                ("Total Equity", "$0.00", "equity"),
                ("Free Balance", "$0.00", "free"),
                ("Unrealized P&L", "$0.00", "pnl"),
                ("Margin Used", "0%", "margin")
            ]
        )
        metrics_layout.addWidget(self.account_card)
        
        # Performance card
        self.performance_card = self.create_metric_card(
            "Today's Performance",
            [
                ("Total P&L", "$0.00", "pnl"),
                ("Win Rate", "0%", "neutral"),
                ("Best Trade", "$0.00", "profit"),
                ("Worst Trade", "$0.00", "loss")
            ]
        )
        metrics_layout.addWidget(self.performance_card)
        
        # Market overview card
        self.market_card = self.create_metric_card(
            "Market Overview",
            [
                ("BTC Price", "$0.00", "neutral"),
                ("ETH Price", "$0.00", "neutral"),
                ("Fear & Greed", "0", "neutral"),
                ("Active Positions", "0", "neutral")
            ]
        )
        metrics_layout.addWidget(self.market_card)
        
        layout.addLayout(metrics_layout)
        
        # Charts section
        charts_splitter = QSplitter(Qt.Horizontal)
        
        # P&L chart
        pnl_chart = self.create_pnl_chart()
        charts_splitter.addWidget(pnl_chart)
        
        # Asset allocation chart
        allocation_chart = self.create_allocation_chart()
        charts_splitter.addWidget(allocation_chart)
        
        layout.addWidget(charts_splitter, 1)
        
        # Recent activity table
        activity_table = self.create_activity_table()
        layout.addWidget(activity_table)
    
    def create_metric_card(self, title, metrics):
        """Create a modern metric card"""
        card = QGroupBox(title)
        card.setStyleSheet("""
            QGroupBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #2d2d2d, stop:1 #1a1a1a);
                border: 1px solid #404040;
                border-radius: 12px;
                font-weight: bold;
                font-size: 14px;
                padding-top: 20px;
                margin: 5px;
            }
            QGroupBox::title {
                color: #00ff44;
                background-color: transparent;
                padding: 0 10px;
            }
        """)
        
        layout = QGridLayout(card)
        
        for i, (label, value, status) in enumerate(metrics):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("color: #cccccc; font-size: 12px;")
            
            value_widget = QLabel(value)
            value_widget.setProperty('status', status)
            value_widget.setStyleSheet(f"""
                font-size: 16px;
                font-weight: bold;
                color: {'#28a745' if status == 'profit' else '#dc3545' if status == 'loss' else '#00ff44' if status == 'equity' else '#0099ff' if status == 'free' else '#cccccc'};
            """)
            
            row = i // 2
            col = (i % 2) * 2
            
            layout.addWidget(label_widget, row * 2, col)
            layout.addWidget(value_widget, row * 2 + 1, col)
        
        return card
    
    def create_pnl_chart(self):
        """Create P&L performance chart"""
        chart_widget = QGroupBox("P&L Performance")
        chart_widget.setStyleSheet("""
            QGroupBox {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                border-radius: 8px;
                color: #00ff44;
                font-weight: bold;
            }
        """)
        
        layout = QVBoxLayout(chart_widget)
        
        # Create plot widget
        plot_widget = pg.PlotWidget()
        plot_widget.setBackground('#1a1a1a')
        plot_widget.setLabel('left', 'P&L ($)', color='#cccccc')
        plot_widget.setLabel('bottom', 'Time', color='#cccccc')
        plot_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # Style the plot
        plot_widget.getAxis('left').setPen(pg.mkPen('#404040'))
        plot_widget.getAxis('bottom').setPen(pg.mkPen('#404040'))
        plot_widget.getAxis('left').setTextPen(pg.mkPen('#cccccc'))
        plot_widget.getAxis('bottom').setTextPen(pg.mkPen('#cccccc'))
        
        layout.addWidget(plot_widget)
        
        self.pnl_plot = plot_widget
        return chart_widget
    
    def create_allocation_chart(self):
        """Create asset allocation pie chart"""
        chart_widget = QGroupBox("Position Allocation")
        chart_widget.setStyleSheet("""
            QGroupBox {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                border-radius: 8px;
                color: #00ff44;
                font-weight: bold;
            }
        """)
        
        layout = QVBoxLayout(chart_widget)
        
        # For now, add a placeholder - you can integrate a proper pie chart library
        placeholder = QLabel("Asset Allocation Chart\n(Pie chart placeholder)")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("color: #cccccc; font-size: 14px;")
        layout.addWidget(placeholder)
        
        return chart_widget
    
    def create_activity_table(self):
        """Create recent activity table"""
        table_widget = QGroupBox("Recent Activity")
        table_widget.setStyleSheet("""
            QGroupBox {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                border-radius: 8px;
                color: #00ff44;
                font-weight: bold;
            }
        """)
        
        layout = QVBoxLayout(table_widget)
        
        table = QTableWidget(0, 6)
        table.setHorizontalHeaderLabels([
            "Time", "Symbol", "Side", "Quantity", "Price", "P&L"
        ])
        
        # Style the table
        table.setStyleSheet("""
            QTableWidget {
                background-color: #1a1a1a;
                gridline-color: #404040;
                border: none;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
            }
            QHeaderView::section {
                background-color: #2d2d2d;
                color: #00ff44;
                font-weight: bold;
                padding: 10px;
                border: none;
                border-right: 1px solid #404040;
            }
        """)
        
        # Set column widths
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(table)
        
        self.activity_table = table
        return table_widget

class EnhancedOrderBookWidget(QWidget):
    """Enhanced order book with better visualization"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_enhanced_orderbook()
    
    def setup_enhanced_orderbook(self):
        """Setup enhanced order book layout"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("Order Book")
        header.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #28a745, stop:0.5 #ffc107, stop:1 #dc3545);
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 8px;
                border-radius: 4px;
                text-align: center;
            }
        """)
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        # Order book table
        self.orderbook_table = QTableWidget(0, 3)
        self.orderbook_table.setHorizontalHeaderLabels(["Price", "Size", "Total"])
        
        # Enhanced styling
        self.orderbook_table.setStyleSheet("""
            QTableWidget {
                background-color: #1a1a1a;
                gridline-color: transparent;
                border: none;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 4px 8px;
                border: none;
            }
            QHeaderView::section {
                background-color: #2d2d2d;
                color: #cccccc;
                font-weight: bold;
                padding: 8px;
                border: none;
            }
        """)
        
        # Remove vertical headers
        self.orderbook_table.verticalHeader().setVisible(False)
        
        # Set equal column widths
        header = self.orderbook_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.orderbook_table)

class TradingControlPanel(QWidget):
    """Enhanced trading control panel with better UX"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_trading_panel()
    
    def setup_trading_panel(self):
        """Setup enhanced trading control panel"""
        layout = QVBoxLayout(self)
        
        # Quick stats at top
        stats_card = QGroupBox("Quick Stats")
        stats_layout = QGridLayout(stats_card)
        
        self.equity_label = QLabel("$0.00")
        self.equity_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00ff44;")
        stats_layout.addWidget(QLabel("Equity:"), 0, 0)
        stats_layout.addWidget(self.equity_label, 0, 1)
        
        self.pnl_label = QLabel("$0.00")
        self.pnl_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        stats_layout.addWidget(QLabel("Unrealized P&L:"), 1, 0)
        stats_layout.addWidget(self.pnl_label, 1, 1)
        
        layout.addWidget(stats_card)
        
        # Trading inputs
        inputs_card = QGroupBox("Trading")
        inputs_layout = QFormLayout(inputs_card)
        
        # Symbol selector with search
        self.symbol_combo = QComboBox()
        self.symbol_combo.setEditable(True)
        self.symbol_combo.setInsertPolicy(QComboBox.NoInsert)
        inputs_layout.addRow("Symbol:", self.symbol_combo)
        
        # Quantity with presets
        qty_layout = QHBoxLayout()
        self.qty_spinbox = QDoubleSpinBox()
        self.qty_spinbox.setDecimals(4)
        self.qty_spinbox.setRange(0.0001, 1000000)
        qty_layout.addWidget(self.qty_spinbox)
        
        # Quick quantity buttons
        for pct in [25, 50, 75, 100]:
            btn = QPushButton(f"{pct}%")
            btn.setFixedWidth(40)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #333;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 4px;
                    color: #cccccc;
                }
                QPushButton:hover {
                    background-color: #555;
                }
            """)
            qty_layout.addWidget(btn)
        
        inputs_layout.addRow("Quantity:", qty_layout)
        
        # Leverage slider
        lev_layout = QVBoxLayout()
        self.leverage_slider = QSlider(Qt.Horizontal)
        self.leverage_slider.setRange(1, 100)
        self.leverage_slider.setValue(10)
        self.leverage_label = QLabel("10x")
        self.leverage_label.setAlignment(Qt.AlignCenter)
        lev_layout.addWidget(self.leverage_slider)
        lev_layout.addWidget(self.leverage_label)
        inputs_layout.addRow("Leverage:", lev_layout)
        
        layout.addWidget(inputs_card)
        
        # Trading buttons
        buttons_card = QGroupBox("Actions")
        buttons_layout = QGridLayout(buttons_card)
        
        # Buy buttons
        buy_market_btn = QPushButton("Market Buy")
        buy_market_btn.setProperty("action", "buy")
        buy_market_btn.setMinimumHeight(40)
        buttons_layout.addWidget(buy_market_btn, 0, 0)
        
        buy_limit_btn = QPushButton("Limit Buy")
        buy_limit_btn.setProperty("action", "buy")
        buy_limit_btn.setMinimumHeight(40)
        buttons_layout.addWidget(buy_limit_btn, 0, 1)
        
        # Sell buttons
        sell_market_btn = QPushButton("Market Sell")
        sell_market_btn.setProperty("action", "sell")
        sell_market_btn.setMinimumHeight(40)
        buttons_layout.addWidget(sell_market_btn, 1, 0)
        
        sell_limit_btn = QPushButton("Limit Sell")
        sell_limit_btn.setProperty("action", "sell")
        sell_limit_btn.setMinimumHeight(40)
        buttons_layout.addWidget(sell_limit_btn, 1, 1)
        
        # Management buttons
        close_all_btn = QPushButton("Close All Positions")
        close_all_btn.setStyleSheet("""
            background-color: #ff6600;
            color: white;
            font-weight: bold;
            border-radius: 6px;
            padding: 8px;
            min-height: 30px;
        """)
        buttons_layout.addWidget(close_all_btn, 2, 0, 1, 2)
        
        layout.addWidget(buttons_card)
        
        layout.addStretch()

# Risk management widget
class RiskManagementWidget(QWidget):
    """Advanced risk management controls"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_risk_management()
    
    def setup_risk_management(self):
        """Setup risk management interface"""
        layout = QVBoxLayout(self)
        
        # Risk metrics card
        risk_card = QGroupBox("Risk Management")
        risk_layout = QFormLayout(risk_card)
        
        # Stop loss
        self.stop_loss_spinbox = QDoubleSpinBox()
        self.stop_loss_spinbox.setDecimals(4)
        self.stop_loss_spinbox.setStyleSheet("color: #dc3545;")
        risk_layout.addRow("Stop Loss:", self.stop_loss_spinbox)
        
        # Take profit
        self.take_profit_spinbox = QDoubleSpinBox()
        self.take_profit_spinbox.setDecimals(4)
        self.take_profit_spinbox.setStyleSheet("color: #28a745;")
        risk_layout.addRow("Take Profit:", self.take_profit_spinbox)
        
        # Max risk per trade
        self.max_risk_spinbox = QDoubleSpinBox()
        self.max_risk_spinbox.setRange(0.1, 10.0)
        self.max_risk_spinbox.setValue(2.0)
        self.max_risk_spinbox.setSuffix("%")
        risk_layout.addRow("Max Risk:", self.max_risk_spinbox)
        
        # Risk/Reward ratio
        self.risk_reward_label = QLabel("1:2")
        self.risk_reward_label.setStyleSheet("font-weight: bold; color: #00ff44;")
        risk_layout.addRow("Risk:Reward:", self.risk_reward_label)
        
        layout.addWidget(risk_card)
        
        # Position size calculator
        calc_card = QGroupBox("Position Calculator")
        calc_layout = QFormLayout(calc_card)
        
        self.account_risk_label = QLabel("$0.00")
        calc_layout.addRow("Account Risk:", self.account_risk_label)
        
        self.position_size_label = QLabel("0.0000")
        calc_layout.addRow("Position Size:", self.position_size_label)
        
        layout.addWidget(calc_card)