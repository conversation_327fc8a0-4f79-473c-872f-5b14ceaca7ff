# UI Improvements for EpiNn0x Trader Interface
# Modern styling and theme system

MODERN_THEMES = {
    'dark_professional': {
        'name': 'Dark Professional',
        'background': '#1a1a1a',
        'surface': '#2d2d2d',
        'primary': '#00ff44',
        'secondary': '#0099ff',
        'accent': '#ff6600',
        'text': '#ffffff',
        'text_secondary': '#cccccc',
        'border': '#404040',
        'hover': '#404040',
        'selection': '#0099ff33'
    },
    'cyberpunk': {
        'name': 'Cyberpunk',
        'background': '#0a0a0a',
        'surface': '#1a1a2e',
        'primary': '#00ff41',
        'secondary': '#ff0080',
        'accent': '#00d4ff',
        'text': '#ffffff',
        'text_secondary': '#b3b3b3',
        'border': '#16213e',
        'hover': '#0f3460',
        'selection': '#ff008033'
    },
    'modern_light': {
        'name': 'Modern Light',
        'background': '#ffffff',
        'surface': '#f5f5f5',
        'primary': '#2196f3',
        'secondary': '#4caf50',
        'accent': '#ff9800',
        'text': '#212121',
        'text_secondary': '#757575',
        'border': '#e0e0e0',
        'hover': '#f0f0f0',
        'selection': '#2196f333'
    }
}

def generate_modern_stylesheet(theme_name='dark_professional'):
    """Generate modern stylesheet with improved visual hierarchy"""
    theme = MODERN_THEMES[theme_name]
    
    return f"""
    /* Main Window */
    QMainWindow {{
        background-color: {theme['background']};
        color: {theme['text']};
        font-family: 'Segoe UI', Arial, sans-serif;
        font-size: 13px;
    }}
    
    /* Improved Buttons */
    QPushButton {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {theme['surface']}, 
                                   stop:1 {lighten_color(theme['surface'], 10)});
        border: 1px solid {theme['border']};
        color: {theme['text']};
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        min-height: 20px;
    }}
    
    QPushButton:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {theme['hover']}, 
                                   stop:1 {lighten_color(theme['hover'], 15)});
        border-color: {theme['primary']};
    }}
    
    QPushButton:pressed {{
        background: {darken_color(theme['surface'], 20)};
        border-color: {theme['primary']};
    }}
    
    /* Trading Action Buttons */
    QPushButton[action="buy"] {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 #28a745, stop:1 #1e7e34);
        border: 1px solid #1e7e34;
        color: white;
        font-weight: bold;
    }}
    
    QPushButton[action="buy"]:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 #34ce57, stop:1 #28a745);
    }}
    
    QPushButton[action="sell"] {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 #dc3545, stop:1 #bd2130);
        border: 1px solid #bd2130;
        color: white;
        font-weight: bold;
    }}
    
    QPushButton[action="sell"]:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 #e55564, stop:1 #dc3545);
    }}
    
    /* Modern Cards/Groups */
    QGroupBox {{
        background-color: {theme['surface']};
        border: 1px solid {theme['border']};
        border-radius: 8px;
        font-weight: bold;
        font-size: 14px;
        padding-top: 20px;
        margin-top: 10px;
        color: {theme['text']};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        subcontrol-position: top left;
        padding: 0 10px;
        color: {theme['primary']};
        background-color: {theme['surface']};
        border-radius: 4px;
    }}
    
    /* Enhanced Tables */
    QTableWidget {{
        background-color: {theme['surface']};
        alternate-background-color: {lighten_color(theme['surface'], 5)};
        gridline-color: {theme['border']};
        border: 1px solid {theme['border']};
        border-radius: 6px;
        selection-background-color: {theme['selection']};
    }}
    
    QTableWidget::item {{
        padding: 8px;
        border-bottom: 1px solid {theme['border']};
    }}
    
    QTableWidget::item:selected {{
        background-color: {theme['selection']};
        color: {theme['text']};
    }}
    
    QHeaderView::section {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {theme['surface']}, 
                                   stop:1 {darken_color(theme['surface'], 10)});
        color: {theme['primary']};
        padding: 10px;
        border: none;
        border-right: 1px solid {theme['border']};
        font-weight: bold;
        text-align: center;
    }}
    
    /* Modern Tabs */
    QTabWidget::pane {{
        border: 1px solid {theme['border']};
        border-radius: 8px;
        background-color: {theme['surface']};
    }}
    
    QTabBar::tab {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {theme['surface']}, 
                                   stop:1 {darken_color(theme['surface'], 10)});
        border: 1px solid {theme['border']};
        padding: 12px 20px;
        margin-right: 2px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        color: {theme['text_secondary']};
        font-weight: 500;
    }}
    
    QTabBar::tab:selected {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {theme['primary']}, 
                                   stop:1 {darken_color(theme['primary'], 20)});
        color: {theme['background']};
        font-weight: bold;
    }}
    
    QTabBar::tab:hover:!selected {{
        background: {theme['hover']};
        color: {theme['text']};
    }}
    
    /* Enhanced Input Fields */
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
        background-color: {theme['surface']};
        border: 2px solid {theme['border']};
        border-radius: 6px;
        padding: 8px;
        color: {theme['text']};
        font-size: 13px;
    }}
    
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
        border-color: {theme['primary']};
        background-color: {lighten_color(theme['surface'], 5)};
    }}
    
    /* Status indicators */
    QLabel[status="profit"] {{
        color: #28a745;
        font-weight: bold;
    }}
    
    QLabel[status="loss"] {{
        color: #dc3545;
        font-weight: bold;
    }}
    
    QLabel[status="neutral"] {{
        color: {theme['text_secondary']};
    }}
    
    /* Toolbar improvements */
    QToolBar {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {theme['surface']}, 
                                   stop:1 {darken_color(theme['surface'], 5)});
        border: none;
        border-bottom: 1px solid {theme['border']};
        padding: 4px;
        spacing: 8px;
    }}
    
    QToolButton {{
        background: transparent;
        border: 1px solid transparent;
        border-radius: 4px;
        padding: 6px 12px;
        color: {theme['text']};
    }}
    
    QToolButton:hover {{
        background-color: {theme['hover']};
        border-color: {theme['border']};
    }}
    """

def lighten_color(color, percent):
    """Lighten a hex color by percentage"""
    # Simple implementation - you'd want a more robust color manipulation
    return color

def darken_color(color, percent):
    """Darken a hex color by percentage"""
    # Simple implementation - you'd want a more robust color manipulation
    return color

# Animation and transition effects
ANIMATION_SETTINGS = {
    'duration': 200,
    'easing': 'ease-in-out',
    'fade_duration': 150
}