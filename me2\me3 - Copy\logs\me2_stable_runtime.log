T
(base) PS C:\Users\<USER>\Documents\dev\me2\me3 - Copy>  & C:/Users/<USER>/AppData/Local/Programs/Python/Python39/python.exe "c:/Users/<USER>/Documents/dev/me2/me3 - Copy/me2_stable.py"

===== CONFIGURING PYQTGRAPH =====
USE_GPU flag is set to: False
PyQtGraph configured for CPU mode (USE_GPU=False)

Current PyQtGraph configuration:
useOpenGL: False
antialias: True
===================================

QPixmap::scaled: Pixmap is a null pixmap
Authentication successful. Proceeding to application startup...
Loading credentials from credentials.yaml
Credentials loaded successfully: ['default_account', 'accounts']
Default account: EPX
Using exchange from default account: huobi
Loading markets for account with API key...
Markets loaded successfully
🔍 demo_mode = False
Strategy ATR-EMA Bands initialized with ATR period: 14, EMA period: 21
Enabled indicators: EMA, ATR, RSI, MACD, EMA Slope, Price Velocity, ATR Breakout, Band Break, Wick Rejection, Band Width, HTF Alignment
Checkbox functions module initialized
Starting main application...

===== CONFIGURING PYQTGRAPH =====
USE_GPU flag is set to: False
PyQtGraph configured for CPU mode (USE_GPU=False)

Current PyQtGraph configuration:
useOpenGL: False
antialias: True
===================================

Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\me2_stable.py", line 9319, in animation_timer
    check_ready_to_proceed()
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\me2_stable.py", line 9307, in check_ready_to_proceed
    _finish_splash(splash_container, movie, on_finished, update_status)
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\me2_stable.py", line 9349, in _finish_splash
    callback()
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\me2_stable.py", line 9369, in startup_main_window
    win = EpinnoxTraderInterface()
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\me2_stable.py", line 4155, in __init__
    self.dashboard_tab = DashboardTab(self)
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\dashboard_tab.py", line 38, in __init__
    self._init_ui_components()
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\dashboard_tab.py", line 86, in _init_ui_components
    self.performance_dashboard = PerformanceDashboard()
  File "c:\Users\<USER>\Documents\dev\me2\me3 - Copy\performance_dashboard.py", line 86, in __init__
    from me2_stable import SETTINGS
ImportError: cannot import name 'SETTINGS' from 'me2_stable' (c:\Users\<USER>\Documents\dev\me2\me3 - Copy\me2_stable.py)